root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-03 00:15:36
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 35
fyers_config - INFO - LTP filter: 2500 - 9000
fyers_config - INFO - Initializing Index Scanner...
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - 
=== Fyers API Authentication ===
fyers_config - INFO - A browser window will open for you to log in to Fyers.
fyers_config - INFO - After logging in, you will be redirected to Google.
fyers_config - INFO - Copy the auth code from the URL and paste it here.
fyers_config - INFO - 
Please login in the private browser window that opened.
fyers_config - ERROR - Error during Fyers authentication: EOF when reading a line
fyers_client - ERROR - Failed to get access token
index_scanner - ERROR - Failed to authenticate with Fyers API
fyers_config - WARNING - No symbols found matching the criteria
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-03 00:16:38
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 35
fyers_config - INFO - LTP filter: 2500 - 9000
fyers_config - INFO - Initializing Index Scanner...
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - 
=== Fyers API Authentication ===
fyers_config - INFO - A browser window will open for you to log in to Fyers.
fyers_config - INFO - After logging in, you will be redirected to Google.
fyers_config - INFO - Copy the auth code from the URL and paste it here.
fyers_config - INFO - 
Please login in the private browser window that opened.
fyers_config - INFO - Authentication files saved to C:\Users\<USER>\Desktop\Python\index_scanner
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
index_scanner - ERROR - Error during scanning: 'SymbolParser' object has no attribute 'get_symbols_for_scanning'
fyers_config - WARNING - No symbols found matching the criteria
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-03 00:24:58
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 35
fyers_config - INFO - LTP filter: 2500 - 9000
fyers_config - INFO - Initializing Index Scanner...
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3556 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3556 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 3556 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3556 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3556 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Saved 3556 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 3556 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 50 symbols
fyers_client - INFO - Fetching quotes for batch 48: 50 symbols
fyers_client - INFO - Fetching quotes for batch 49: 50 symbols
fyers_client - INFO - Fetching quotes for batch 50: 50 symbols
fyers_client - INFO - Fetching quotes for batch 51: 50 symbols
fyers_client - INFO - Fetching quotes for batch 52: 50 symbols
fyers_client - INFO - Fetching quotes for batch 53: 50 symbols
fyers_client - INFO - Fetching quotes for batch 54: 50 symbols
fyers_client - INFO - Fetching quotes for batch 55: 50 symbols
fyers_client - INFO - Fetching quotes for batch 56: 50 symbols
fyers_client - INFO - Fetching quotes for batch 57: 50 symbols
fyers_client - INFO - Fetching quotes for batch 58: 50 symbols
fyers_client - INFO - Fetching quotes for batch 59: 50 symbols
fyers_client - INFO - Fetching quotes for batch 60: 50 symbols
fyers_client - INFO - Fetching quotes for batch 61: 50 symbols
fyers_client - INFO - Fetching quotes for batch 62: 50 symbols
fyers_client - INFO - Fetching quotes for batch 63: 50 symbols
fyers_client - INFO - Fetching quotes for batch 64: 50 symbols
fyers_client - INFO - Fetching quotes for batch 65: 50 symbols
fyers_client - INFO - Fetching quotes for batch 66: 50 symbols
fyers_client - INFO - Fetching quotes for batch 67: 50 symbols
fyers_client - INFO - Fetching quotes for batch 68: 50 symbols
fyers_client - INFO - Fetching quotes for batch 69: 50 symbols
fyers_client - INFO - Fetching quotes for batch 70: 50 symbols
fyers_client - INFO - Fetching quotes for batch 71: 50 symbols
fyers_client - INFO - Fetching quotes for batch 72: 6 symbols
fyers_client - INFO - Successfully fetched market data for 3556 symbols
index_scanner - INFO - Received market data for 3556 symbols
index_scanner - INFO - Applying filters...
index_scanner - INFO - Applying filters - Volume Range: 35-*********, LTP Range: 2500-9000
index_scanner - INFO - EMA Analysis: Enabled
index_scanner - INFO - EMA Periods - Short: 9, Long: 21
index_scanner - INFO - Timeframe - Interval: 60, Days: 15
index_scanner - INFO - First symbol for BANKNIFTY JUL: NSE:BANKNIFTY25JUL48500CE (LTP: 8869.1, Volume: 385)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL48500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 32 OHLC data points for NSE:BANKNIFTY25JUL48500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL49000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:BANKNIFTY25JUL49000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL49500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 55 OHLC data points for NSE:BANKNIFTY25JUL49500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL50000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL50000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL51000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 63 OHLC data points for NSE:BANKNIFTY25JUL51500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL52000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL52000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL52500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 63 OHLC data points for NSE:BANKNIFTY25JUL52500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL53000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:BANKNIFTY25JUL53500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54200CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 31 OHLC data points for NSE:BANKNIFTY25JUL54200CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54300CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 28 OHLC data points for NSE:BANKNIFTY25JUL54300CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54400CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 26 OHLC data points for NSE:BANKNIFTY25JUL54400CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL54500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54700CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 18 OHLC data points for NSE:BANKNIFTY25JUL54700CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54800CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 35 OHLC data points for NSE:BANKNIFTY25JUL54800CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL59700PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 12 OHLC data points for NSE:BANKNIFTY25JUL59700PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL59900PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 13 OHLC data points for NSE:BANKNIFTY25JUL59900PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL60000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL60000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL61000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:BANKNIFTY25JUL61000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL62000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 70 OHLC data points for NSE:BANKNIFTY25JUL62000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL63000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:BANKNIFTY25JUL63000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL64000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 61 OHLC data points for NSE:BANKNIFTY25JUL64000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL64500PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 27 OHLC data points for NSE:BANKNIFTY25JUL64500PE
index_scanner - INFO - First symbol for NIFTY JUL: NSE:NIFTY25JUL22300CE (LTP: 3200.0, Volume: 825)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22300CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 68 OHLC data points for NSE:NIFTY25JUL22300CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22350CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:NIFTY25JUL22350CE
index_scanner - WARNING - Insufficient historical data for EMA calculation: NSE:NIFTY25JUL22350CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22400CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 32 OHLC data points for NSE:NIFTY25JUL22400CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25JUL22500CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22550CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:NIFTY25JUL22550CE
index_scanner - WARNING - Insufficient historical data for EMA calculation: NSE:NIFTY25JUL22550CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22600CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 30 OHLC data points for NSE:NIFTY25JUL22600CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22650CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 17 OHLC data points for NSE:NIFTY25JUL22650CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22700CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 45 OHLC data points for NSE:NIFTY25JUL22700CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22800CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 34 OHLC data points for NSE:NIFTY25JUL22800CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22900CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 25 OHLC data points for NSE:NIFTY25JUL22900CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25JUL23000CE
index_scanner - INFO - First symbol for BANKNIFTY AUG: NSE:BANKNIFTY25AUG50000CE (LTP: 7630.0, Volume: 420)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG50000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 53 OHLC data points for NSE:BANKNIFTY25AUG50000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG53000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 48 OHLC data points for NSE:BANKNIFTY25AUG53000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 67 OHLC data points for NSE:BANKNIFTY25AUG54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG55000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:BANKNIFTY25AUG55000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG60000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 46 OHLC data points for NSE:BANKNIFTY25AUG60000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG62000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 16 OHLC data points for NSE:BANKNIFTY25AUG62000PE
index_scanner - INFO - First symbol for NIFTY AUG: NSE:NIFTY25AUG23000CE (LTP: 2613.6, Volume: 600)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25AUG23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 79 OHLC data points for NSE:NIFTY25AUG23000CE
index_scanner - INFO - First symbol for BANKNIFTY SEP: NSE:BANKNIFTY25SEP55000CE (LTP: 3149.75, Volume: 35)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP55000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 9 OHLC data points for NSE:BANKNIFTY25SEP55000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP52500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 54 OHLC data points for NSE:BANKNIFTY25SEP52500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 60 OHLC data points for NSE:BANKNIFTY25SEP54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP55500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25SEP55500CE
index_scanner - INFO - First symbol for NIFTY SEP: NSE:NIFTY25SEP17000CE (LTP: 8595.2, Volume: 3000)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP17000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 50 OHLC data points for NSE:NIFTY25SEP17000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP18000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 51 OHLC data points for NSE:NIFTY25SEP18000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP20000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:NIFTY25SEP20000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP21000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 71 OHLC data points for NSE:NIFTY25SEP21000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP22000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:NIFTY25SEP22000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:NIFTY25SEP23000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP29000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25SEP29000PE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP30000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 23 OHLC data points for NSE:NIFTY25SEP30000PE
index_scanner - INFO - Filtering Results:
index_scanner - INFO -   Total symbols processed: 3556
index_scanner - INFO -   Passed volume filter: 804
index_scanner - INFO -   Passed LTP filter: 55
index_scanner - INFO -   Passed EMA filter: 11
index_scanner - INFO -   Final symbols after all filters: 11
index_scanner - INFO -   Overall success rate: 0.31%
index_scanner - INFO - Scanning complete. Found 11 symbols matching criteria
fyers_config - INFO - Generating summary statistics...
fyers_config - INFO - Generating reports...
report_generator - INFO - Output directory ready: reports
report_generator - INFO - Sorted 11 symbols by underlying and month sequence
report_generator - INFO - CSV report created successfully: reports\index_scan_20250703_002527.csv
report_generator - INFO - Report contains 11 symbols
report_generator - INFO - Summary report created successfully: reports\index_scan_summary_20250703_002527.txt
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER COMPLETED SUCCESSFULLY
fyers_config - INFO - ================================================================================
fyers_config - INFO - End time: 2025-07-03 00:25:27
fyers_config - INFO - CSV Report: reports\index_scan_20250703_002527.csv
fyers_config - INFO - Summary Report: reports\index_scan_summary_20250703_002527.txt
fyers_config - INFO - Total symbols found: 11
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-03 01:00:36
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 35
fyers_config - INFO - LTP filter: 2500 - 9000
fyers_config - INFO - Initializing Index Scanner...
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3556 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3556 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 3556 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3556 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3556 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Saved 3556 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 3556 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 50 symbols
fyers_client - INFO - Fetching quotes for batch 48: 50 symbols
fyers_client - INFO - Fetching quotes for batch 49: 50 symbols
fyers_client - INFO - Fetching quotes for batch 50: 50 symbols
fyers_client - INFO - Fetching quotes for batch 51: 50 symbols
fyers_client - INFO - Fetching quotes for batch 52: 50 symbols
fyers_client - INFO - Fetching quotes for batch 53: 50 symbols
fyers_client - INFO - Fetching quotes for batch 54: 50 symbols
fyers_client - INFO - Fetching quotes for batch 55: 50 symbols
fyers_client - INFO - Fetching quotes for batch 56: 50 symbols
fyers_client - INFO - Fetching quotes for batch 57: 50 symbols
fyers_client - INFO - Fetching quotes for batch 58: 50 symbols
fyers_client - INFO - Fetching quotes for batch 59: 50 symbols
fyers_client - INFO - Fetching quotes for batch 60: 50 symbols
fyers_client - INFO - Fetching quotes for batch 61: 50 symbols
fyers_client - INFO - Fetching quotes for batch 62: 50 symbols
fyers_client - INFO - Fetching quotes for batch 63: 50 symbols
fyers_client - INFO - Fetching quotes for batch 64: 50 symbols
fyers_client - INFO - Fetching quotes for batch 65: 50 symbols
fyers_client - INFO - Fetching quotes for batch 66: 50 symbols
fyers_client - INFO - Fetching quotes for batch 67: 50 symbols
fyers_client - INFO - Fetching quotes for batch 68: 50 symbols
fyers_client - INFO - Fetching quotes for batch 69: 50 symbols
fyers_client - INFO - Fetching quotes for batch 70: 50 symbols
fyers_client - INFO - Fetching quotes for batch 71: 50 symbols
fyers_client - INFO - Fetching quotes for batch 72: 6 symbols
fyers_client - INFO - Successfully fetched market data for 3556 symbols
index_scanner - INFO - Received market data for 3556 symbols
index_scanner - ERROR - Error during scanning: 'ConfigLoader' object has no attribute 'ema_enabled'
fyers_config - WARNING - No symbols found matching the criteria
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-03 01:11:11
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 35
fyers_config - INFO - LTP filter: 2500 - 9000
fyers_config - INFO - Initializing Index Scanner...
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3556 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3556 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 3556 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3556 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3556 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Saved 3556 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 3556 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 50 symbols
fyers_client - INFO - Fetching quotes for batch 48: 50 symbols
fyers_client - INFO - Fetching quotes for batch 49: 50 symbols
fyers_client - INFO - Fetching quotes for batch 50: 50 symbols
fyers_client - INFO - Fetching quotes for batch 51: 50 symbols
fyers_client - INFO - Fetching quotes for batch 52: 50 symbols
fyers_client - INFO - Fetching quotes for batch 53: 50 symbols
fyers_client - INFO - Fetching quotes for batch 54: 50 symbols
fyers_client - INFO - Fetching quotes for batch 55: 50 symbols
fyers_client - INFO - Fetching quotes for batch 56: 50 symbols
fyers_client - INFO - Fetching quotes for batch 57: 50 symbols
fyers_client - INFO - Fetching quotes for batch 58: 50 symbols
fyers_client - INFO - Fetching quotes for batch 59: 50 symbols
fyers_client - INFO - Fetching quotes for batch 60: 50 symbols
fyers_client - INFO - Fetching quotes for batch 61: 50 symbols
fyers_client - INFO - Fetching quotes for batch 62: 50 symbols
fyers_client - INFO - Fetching quotes for batch 63: 50 symbols
fyers_client - INFO - Fetching quotes for batch 64: 50 symbols
fyers_client - INFO - Fetching quotes for batch 65: 50 symbols
fyers_client - INFO - Fetching quotes for batch 66: 50 symbols
fyers_client - INFO - Fetching quotes for batch 67: 50 symbols
fyers_client - INFO - Fetching quotes for batch 68: 50 symbols
fyers_client - INFO - Fetching quotes for batch 69: 50 symbols
fyers_client - INFO - Fetching quotes for batch 70: 50 symbols
fyers_client - INFO - Fetching quotes for batch 71: 50 symbols
fyers_client - INFO - Fetching quotes for batch 72: 6 symbols
fyers_client - INFO - Successfully fetched market data for 3556 symbols
index_scanner - INFO - Received market data for 3556 symbols
index_scanner - INFO - Applying filters...
index_scanner - INFO - Applying filters - Volume Range: 35-*********, LTP Range: 2500-9000
index_scanner - INFO - MAE Analysis: Enabled
index_scanner - INFO - MAE Length: 9, Source: close, Offset: 0, Smoothing: ema(9)
index_scanner - INFO - Timeframe - Interval: 60, Days: 15
index_scanner - INFO - First symbol for BANKNIFTY JUL: NSE:BANKNIFTY25JUL48500CE (LTP: 8869.1, Volume: 385)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL48500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 32 OHLC data points for NSE:BANKNIFTY25JUL48500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL49000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:BANKNIFTY25JUL49000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL49500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 55 OHLC data points for NSE:BANKNIFTY25JUL49500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL50000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL50000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL51000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 63 OHLC data points for NSE:BANKNIFTY25JUL51500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL52000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL52000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL52500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 63 OHLC data points for NSE:BANKNIFTY25JUL52500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL53000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:BANKNIFTY25JUL53500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54200CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 31 OHLC data points for NSE:BANKNIFTY25JUL54200CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54300CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 28 OHLC data points for NSE:BANKNIFTY25JUL54300CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54400CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 26 OHLC data points for NSE:BANKNIFTY25JUL54400CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL54500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54700CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 18 OHLC data points for NSE:BANKNIFTY25JUL54700CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54800CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 35 OHLC data points for NSE:BANKNIFTY25JUL54800CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL59700PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 12 OHLC data points for NSE:BANKNIFTY25JUL59700PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL59900PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 13 OHLC data points for NSE:BANKNIFTY25JUL59900PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL60000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL60000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL61000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:BANKNIFTY25JUL61000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL62000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 70 OHLC data points for NSE:BANKNIFTY25JUL62000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL63000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:BANKNIFTY25JUL63000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL64000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 61 OHLC data points for NSE:BANKNIFTY25JUL64000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL64500PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 27 OHLC data points for NSE:BANKNIFTY25JUL64500PE
index_scanner - INFO - First symbol for NIFTY JUL: NSE:NIFTY25JUL22300CE (LTP: 3200.0, Volume: 825)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22300CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 68 OHLC data points for NSE:NIFTY25JUL22300CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22350CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:NIFTY25JUL22350CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:NIFTY25JUL22350CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22400CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 32 OHLC data points for NSE:NIFTY25JUL22400CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25JUL22500CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22550CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:NIFTY25JUL22550CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:NIFTY25JUL22550CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22600CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 30 OHLC data points for NSE:NIFTY25JUL22600CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22650CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 17 OHLC data points for NSE:NIFTY25JUL22650CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22700CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 45 OHLC data points for NSE:NIFTY25JUL22700CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22800CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 34 OHLC data points for NSE:NIFTY25JUL22800CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22900CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 25 OHLC data points for NSE:NIFTY25JUL22900CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25JUL23000CE
index_scanner - INFO - First symbol for BANKNIFTY AUG: NSE:BANKNIFTY25AUG50000CE (LTP: 7630.0, Volume: 420)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG50000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 53 OHLC data points for NSE:BANKNIFTY25AUG50000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG53000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 48 OHLC data points for NSE:BANKNIFTY25AUG53000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 67 OHLC data points for NSE:BANKNIFTY25AUG54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG55000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:BANKNIFTY25AUG55000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG60000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 46 OHLC data points for NSE:BANKNIFTY25AUG60000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG62000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 16 OHLC data points for NSE:BANKNIFTY25AUG62000PE
index_scanner - INFO - First symbol for NIFTY AUG: NSE:NIFTY25AUG23000CE (LTP: 2613.6, Volume: 600)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25AUG23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 79 OHLC data points for NSE:NIFTY25AUG23000CE
index_scanner - INFO - First symbol for BANKNIFTY SEP: NSE:BANKNIFTY25SEP55000CE (LTP: 3149.75, Volume: 35)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP55000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 9 OHLC data points for NSE:BANKNIFTY25SEP55000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP52500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 54 OHLC data points for NSE:BANKNIFTY25SEP52500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 60 OHLC data points for NSE:BANKNIFTY25SEP54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP55500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25SEP55500CE
index_scanner - INFO - First symbol for NIFTY SEP: NSE:NIFTY25SEP17000CE (LTP: 8595.2, Volume: 3000)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP17000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 50 OHLC data points for NSE:NIFTY25SEP17000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP18000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 51 OHLC data points for NSE:NIFTY25SEP18000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP20000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:NIFTY25SEP20000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP21000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 71 OHLC data points for NSE:NIFTY25SEP21000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP22000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:NIFTY25SEP22000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:NIFTY25SEP23000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP29000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25SEP29000PE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP30000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 23 OHLC data points for NSE:NIFTY25SEP30000PE
index_scanner - INFO - Filtering Results:
index_scanner - INFO -   Total symbols processed: 3556
index_scanner - INFO -   Passed volume filter: 804
index_scanner - INFO -   Passed LTP filter: 55
index_scanner - INFO -   Passed MAE filter: 8
index_scanner - INFO -   Final symbols after all filters: 8
index_scanner - INFO -   Overall success rate: 0.22%
index_scanner - INFO - Scanning complete. Found 8 symbols matching criteria
fyers_config - INFO - Generating summary statistics...
fyers_config - INFO - Generating reports...
report_generator - INFO - Output directory ready: reports
report_generator - INFO - Sorted 8 symbols by underlying and month sequence
report_generator - INFO - CSV report created successfully: reports\index_scan_20250703_011138.csv
report_generator - INFO - Report contains 8 symbols
report_generator - INFO - Summary report created successfully: reports\index_scan_summary_20250703_011138.txt
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER COMPLETED SUCCESSFULLY
fyers_config - INFO - ================================================================================
fyers_config - INFO - End time: 2025-07-03 01:11:38
fyers_config - INFO - CSV Report: reports\index_scan_20250703_011138.csv
fyers_config - INFO - Summary Report: reports\index_scan_summary_20250703_011138.txt
fyers_config - INFO - Total symbols found: 8
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-03 01:16:15
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 35
fyers_config - INFO - LTP filter: 2500 - 9000
fyers_config - INFO - Initializing Index Scanner...
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3556 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3556 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 3556 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3556 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3556 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Saved 3556 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 3556 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 50 symbols
fyers_client - INFO - Fetching quotes for batch 48: 50 symbols
fyers_client - INFO - Fetching quotes for batch 49: 50 symbols
fyers_client - INFO - Fetching quotes for batch 50: 50 symbols
fyers_client - INFO - Fetching quotes for batch 51: 50 symbols
fyers_client - INFO - Fetching quotes for batch 52: 50 symbols
fyers_client - INFO - Fetching quotes for batch 53: 50 symbols
fyers_client - INFO - Fetching quotes for batch 54: 50 symbols
fyers_client - INFO - Fetching quotes for batch 55: 50 symbols
fyers_client - INFO - Fetching quotes for batch 56: 50 symbols
fyers_client - INFO - Fetching quotes for batch 57: 50 symbols
fyers_client - INFO - Fetching quotes for batch 58: 50 symbols
fyers_client - INFO - Fetching quotes for batch 59: 50 symbols
fyers_client - INFO - Fetching quotes for batch 60: 50 symbols
fyers_client - INFO - Fetching quotes for batch 61: 50 symbols
fyers_client - INFO - Fetching quotes for batch 62: 50 symbols
fyers_client - INFO - Fetching quotes for batch 63: 50 symbols
fyers_client - INFO - Fetching quotes for batch 64: 50 symbols
fyers_client - INFO - Fetching quotes for batch 65: 50 symbols
fyers_client - INFO - Fetching quotes for batch 66: 50 symbols
fyers_client - INFO - Fetching quotes for batch 67: 50 symbols
fyers_client - INFO - Fetching quotes for batch 68: 50 symbols
fyers_client - INFO - Fetching quotes for batch 69: 50 symbols
fyers_client - INFO - Fetching quotes for batch 70: 50 symbols
fyers_client - INFO - Fetching quotes for batch 71: 50 symbols
fyers_client - INFO - Fetching quotes for batch 72: 6 symbols
fyers_client - INFO - Successfully fetched market data for 3556 symbols
index_scanner - INFO - Received market data for 3556 symbols
index_scanner - INFO - Applying filters...
index_scanner - INFO - Applying filters - Volume Range: 35-*********, LTP Range: 2500-9000
index_scanner - INFO - MAE Analysis: Enabled
index_scanner - INFO - MAE Length: 9, Source: close, Offset: 0, Smoothing: ema(9)
index_scanner - INFO - Timeframe - Interval: 60, Days: 15
index_scanner - INFO - First symbol for BANKNIFTY JUL: NSE:BANKNIFTY25JUL48500CE (LTP: 8869.1, Volume: 385)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL48500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 32 OHLC data points for NSE:BANKNIFTY25JUL48500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL49000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:BANKNIFTY25JUL49000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL49500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 55 OHLC data points for NSE:BANKNIFTY25JUL49500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL50000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL50000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL51000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 63 OHLC data points for NSE:BANKNIFTY25JUL51500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL52000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL52000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL52500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 63 OHLC data points for NSE:BANKNIFTY25JUL52500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL53000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:BANKNIFTY25JUL53500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54200CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 31 OHLC data points for NSE:BANKNIFTY25JUL54200CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54300CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 28 OHLC data points for NSE:BANKNIFTY25JUL54300CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54400CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 26 OHLC data points for NSE:BANKNIFTY25JUL54400CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL54500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54700CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 18 OHLC data points for NSE:BANKNIFTY25JUL54700CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54800CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 35 OHLC data points for NSE:BANKNIFTY25JUL54800CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL59700PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 12 OHLC data points for NSE:BANKNIFTY25JUL59700PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL59900PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 13 OHLC data points for NSE:BANKNIFTY25JUL59900PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL60000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL60000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL61000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:BANKNIFTY25JUL61000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL62000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 70 OHLC data points for NSE:BANKNIFTY25JUL62000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL63000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:BANKNIFTY25JUL63000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL64000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 61 OHLC data points for NSE:BANKNIFTY25JUL64000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL64500PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 27 OHLC data points for NSE:BANKNIFTY25JUL64500PE
index_scanner - INFO - First symbol for NIFTY JUL: NSE:NIFTY25JUL22300CE (LTP: 3200.0, Volume: 825)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22300CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 68 OHLC data points for NSE:NIFTY25JUL22300CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22350CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:NIFTY25JUL22350CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:NIFTY25JUL22350CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22400CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 32 OHLC data points for NSE:NIFTY25JUL22400CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25JUL22500CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22550CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:NIFTY25JUL22550CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:NIFTY25JUL22550CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22600CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 30 OHLC data points for NSE:NIFTY25JUL22600CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22650CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 17 OHLC data points for NSE:NIFTY25JUL22650CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22700CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 45 OHLC data points for NSE:NIFTY25JUL22700CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22800CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 34 OHLC data points for NSE:NIFTY25JUL22800CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22900CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 25 OHLC data points for NSE:NIFTY25JUL22900CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25JUL23000CE
index_scanner - INFO - First symbol for BANKNIFTY AUG: NSE:BANKNIFTY25AUG50000CE (LTP: 7630.0, Volume: 420)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG50000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 53 OHLC data points for NSE:BANKNIFTY25AUG50000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG53000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 48 OHLC data points for NSE:BANKNIFTY25AUG53000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 67 OHLC data points for NSE:BANKNIFTY25AUG54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG55000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:BANKNIFTY25AUG55000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG60000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 46 OHLC data points for NSE:BANKNIFTY25AUG60000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG62000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 16 OHLC data points for NSE:BANKNIFTY25AUG62000PE
index_scanner - INFO - First symbol for NIFTY AUG: NSE:NIFTY25AUG23000CE (LTP: 2613.6, Volume: 600)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25AUG23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 79 OHLC data points for NSE:NIFTY25AUG23000CE
index_scanner - INFO - First symbol for BANKNIFTY SEP: NSE:BANKNIFTY25SEP55000CE (LTP: 3149.75, Volume: 35)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP55000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 9 OHLC data points for NSE:BANKNIFTY25SEP55000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP52500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 54 OHLC data points for NSE:BANKNIFTY25SEP52500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 60 OHLC data points for NSE:BANKNIFTY25SEP54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP55500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25SEP55500CE
index_scanner - INFO - First symbol for NIFTY SEP: NSE:NIFTY25SEP17000CE (LTP: 8595.2, Volume: 3000)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP17000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 50 OHLC data points for NSE:NIFTY25SEP17000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP18000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 51 OHLC data points for NSE:NIFTY25SEP18000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP20000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:NIFTY25SEP20000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP21000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 71 OHLC data points for NSE:NIFTY25SEP21000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP22000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:NIFTY25SEP22000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:NIFTY25SEP23000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP29000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25SEP29000PE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP30000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 23 OHLC data points for NSE:NIFTY25SEP30000PE
index_scanner - INFO - Filtering Results:
index_scanner - INFO -   Total symbols processed: 3556
index_scanner - INFO -   Passed volume filter: 804
index_scanner - INFO -   Passed LTP filter: 55
index_scanner - INFO -   Passed MAE filter: 8
index_scanner - INFO -   Final symbols after all filters: 8
index_scanner - INFO -   Overall success rate: 0.22%
index_scanner - INFO - Scanning complete. Found 8 symbols matching criteria
fyers_config - INFO - Generating summary statistics...
fyers_config - INFO - Generating reports...
report_generator - INFO - Output directory ready: reports
report_generator - INFO - Sorted 8 symbols by underlying and month sequence
report_generator - INFO - CSV report created successfully: reports\index_scan_20250703_011643.csv
report_generator - INFO - Report contains 8 symbols
report_generator - INFO - Summary report created successfully: reports\index_scan_summary_20250703_011643.txt
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER COMPLETED SUCCESSFULLY
fyers_config - INFO - ================================================================================
fyers_config - INFO - End time: 2025-07-03 01:16:43
fyers_config - INFO - CSV Report: reports\index_scan_20250703_011643.csv
fyers_config - INFO - Summary Report: reports\index_scan_summary_20250703_011643.txt
fyers_config - INFO - Total symbols found: 8
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-03 01:31:17
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 35
fyers_config - INFO - LTP filter: 2500 - 9000
fyers_config - INFO - Initializing Index Scanner...
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3556 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3556 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 3556 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3556 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3556 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Saved 3556 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 3556 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 50 symbols
fyers_client - INFO - Fetching quotes for batch 48: 50 symbols
fyers_client - INFO - Fetching quotes for batch 49: 50 symbols
fyers_client - INFO - Fetching quotes for batch 50: 50 symbols
fyers_client - INFO - Fetching quotes for batch 51: 50 symbols
fyers_client - INFO - Fetching quotes for batch 52: 50 symbols
fyers_client - INFO - Fetching quotes for batch 53: 50 symbols
fyers_client - INFO - Fetching quotes for batch 54: 50 symbols
fyers_client - INFO - Fetching quotes for batch 55: 50 symbols
fyers_client - INFO - Fetching quotes for batch 56: 50 symbols
fyers_client - INFO - Fetching quotes for batch 57: 50 symbols
fyers_client - INFO - Fetching quotes for batch 58: 50 symbols
fyers_client - INFO - Fetching quotes for batch 59: 50 symbols
fyers_client - INFO - Fetching quotes for batch 60: 50 symbols
fyers_client - INFO - Fetching quotes for batch 61: 50 symbols
fyers_client - INFO - Fetching quotes for batch 62: 50 symbols
fyers_client - INFO - Fetching quotes for batch 63: 50 symbols
fyers_client - INFO - Fetching quotes for batch 64: 50 symbols
fyers_client - INFO - Fetching quotes for batch 65: 50 symbols
fyers_client - INFO - Fetching quotes for batch 66: 50 symbols
fyers_client - INFO - Fetching quotes for batch 67: 50 symbols
fyers_client - INFO - Fetching quotes for batch 68: 50 symbols
fyers_client - INFO - Fetching quotes for batch 69: 50 symbols
fyers_client - INFO - Fetching quotes for batch 70: 50 symbols
fyers_client - INFO - Fetching quotes for batch 71: 50 symbols
fyers_client - INFO - Fetching quotes for batch 72: 6 symbols
fyers_client - INFO - Successfully fetched market data for 3556 symbols
index_scanner - INFO - Received market data for 3556 symbols
index_scanner - INFO - Applying filters...
index_scanner - INFO - Applying filters - Volume Range: 35-*********, LTP Range: 2500-9000
index_scanner - INFO - MAE Analysis: Enabled
index_scanner - INFO - MAE Length: 9, Source: close, Offset: 0, Smoothing: ema(9)
index_scanner - INFO - Timeframe - Interval: 60, Days: 15
index_scanner - INFO - First symbol for BANKNIFTY JUL: NSE:BANKNIFTY25JUL48500CE (LTP: 8869.1, Volume: 385)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL48500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 32 OHLC data points for NSE:BANKNIFTY25JUL48500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL49000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:BANKNIFTY25JUL49000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL49500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 55 OHLC data points for NSE:BANKNIFTY25JUL49500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL50000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL50000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL51000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 63 OHLC data points for NSE:BANKNIFTY25JUL51500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL52000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL52000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL52500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 63 OHLC data points for NSE:BANKNIFTY25JUL52500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL53000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:BANKNIFTY25JUL53500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54200CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 31 OHLC data points for NSE:BANKNIFTY25JUL54200CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54300CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 28 OHLC data points for NSE:BANKNIFTY25JUL54300CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54400CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 26 OHLC data points for NSE:BANKNIFTY25JUL54400CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL54500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54700CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 18 OHLC data points for NSE:BANKNIFTY25JUL54700CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54800CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 35 OHLC data points for NSE:BANKNIFTY25JUL54800CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL59700PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 12 OHLC data points for NSE:BANKNIFTY25JUL59700PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL59900PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 13 OHLC data points for NSE:BANKNIFTY25JUL59900PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL60000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL60000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL61000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:BANKNIFTY25JUL61000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL62000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 70 OHLC data points for NSE:BANKNIFTY25JUL62000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL63000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:BANKNIFTY25JUL63000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL64000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 61 OHLC data points for NSE:BANKNIFTY25JUL64000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL64500PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 27 OHLC data points for NSE:BANKNIFTY25JUL64500PE
index_scanner - INFO - First symbol for NIFTY JUL: NSE:NIFTY25JUL22300CE (LTP: 3200.0, Volume: 825)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22300CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 68 OHLC data points for NSE:NIFTY25JUL22300CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22350CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:NIFTY25JUL22350CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:NIFTY25JUL22350CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22400CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 32 OHLC data points for NSE:NIFTY25JUL22400CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25JUL22500CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22550CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:NIFTY25JUL22550CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:NIFTY25JUL22550CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22600CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 30 OHLC data points for NSE:NIFTY25JUL22600CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22650CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 17 OHLC data points for NSE:NIFTY25JUL22650CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22700CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 45 OHLC data points for NSE:NIFTY25JUL22700CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22800CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 34 OHLC data points for NSE:NIFTY25JUL22800CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22900CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 25 OHLC data points for NSE:NIFTY25JUL22900CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25JUL23000CE
index_scanner - INFO - First symbol for BANKNIFTY AUG: NSE:BANKNIFTY25AUG50000CE (LTP: 7630.0, Volume: 420)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG50000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 53 OHLC data points for NSE:BANKNIFTY25AUG50000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG53000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 48 OHLC data points for NSE:BANKNIFTY25AUG53000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 67 OHLC data points for NSE:BANKNIFTY25AUG54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG55000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:BANKNIFTY25AUG55000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG60000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 46 OHLC data points for NSE:BANKNIFTY25AUG60000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG62000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 16 OHLC data points for NSE:BANKNIFTY25AUG62000PE
index_scanner - INFO - First symbol for NIFTY AUG: NSE:NIFTY25AUG23000CE (LTP: 2613.6, Volume: 600)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25AUG23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 79 OHLC data points for NSE:NIFTY25AUG23000CE
index_scanner - INFO - First symbol for BANKNIFTY SEP: NSE:BANKNIFTY25SEP55000CE (LTP: 3149.75, Volume: 35)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP55000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 9 OHLC data points for NSE:BANKNIFTY25SEP55000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP52500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 54 OHLC data points for NSE:BANKNIFTY25SEP52500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 60 OHLC data points for NSE:BANKNIFTY25SEP54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP55500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25SEP55500CE
index_scanner - INFO - First symbol for NIFTY SEP: NSE:NIFTY25SEP17000CE (LTP: 8595.2, Volume: 3000)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP17000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 50 OHLC data points for NSE:NIFTY25SEP17000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP18000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 51 OHLC data points for NSE:NIFTY25SEP18000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP20000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:NIFTY25SEP20000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP21000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 71 OHLC data points for NSE:NIFTY25SEP21000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP22000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:NIFTY25SEP22000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:NIFTY25SEP23000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP29000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25SEP29000PE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP30000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 23 OHLC data points for NSE:NIFTY25SEP30000PE
index_scanner - INFO - Filtering Results:
index_scanner - INFO -   Total symbols processed: 3556
index_scanner - INFO -   Passed volume filter: 804
index_scanner - INFO -   Passed LTP filter: 55
index_scanner - INFO -   Passed MAE filter: 8
index_scanner - INFO -   Final symbols after all filters: 8
index_scanner - INFO -   Overall success rate: 0.22%
index_scanner - INFO - Scanning complete. Found 8 symbols matching criteria
fyers_config - INFO - Generating summary statistics...
fyers_config - INFO - Generating reports...
report_generator - INFO - Output directory ready: reports
report_generator - INFO - Sorted 8 symbols by underlying and month sequence
report_generator - INFO - CSV report created successfully: reports\index_scan_20250703_013150.csv
report_generator - INFO - Report contains 8 symbols
report_generator - INFO - Summary report created successfully: reports\index_scan_summary_20250703_013150.txt
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER COMPLETED SUCCESSFULLY
fyers_config - INFO - ================================================================================
fyers_config - INFO - End time: 2025-07-03 01:31:50
fyers_config - INFO - CSV Report: reports\index_scan_20250703_013150.csv
fyers_config - INFO - Summary Report: reports\index_scan_summary_20250703_013150.txt
fyers_config - INFO - Total symbols found: 8
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - INDEX SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-03 02:56:30
fyers_config - INFO - Loading configuration...
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\index_scanner\config.yaml
config_loader - INFO - Configuration validation successful
fyers_config - INFO - Configuration loaded and validated successfully
fyers_config - INFO - Target symbols: ['NIFTY', 'BANKNIFTY']
fyers_config - INFO - Volume filter: >= 35
fyers_config - INFO - LTP filter: 2500 - 9000
fyers_config - INFO - Initializing Index Scanner...
fyers_config - INFO - Starting symbol scanning process...
index_scanner - INFO - Starting index scanner...
index_scanner - INFO - Authenticating with Fyers API...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
index_scanner - INFO - Loading symbols from CSV...
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3556 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3556 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Prepared 3556 symbols for scanning
symbol_parser - INFO - Current month expiry (2025-07-31) hasn't passed. Using current + next 2 months.
symbol_parser - INFO - Target months for filtering: ['JUL', 'AUG', 'SEP']
symbol_parser - INFO - Loaded 3556 symbols from CSV for target months
symbol_parser - INFO - Filtered to 3556 symbols for underlyings: ['NIFTY', 'BANKNIFTY']
symbol_parser - INFO - Saved 3556 filtered symbols to reports\filtered_symbols.csv
index_scanner - INFO - Found 3556 symbols to scan
index_scanner - INFO - Fetching market data from Fyers API...
fyers_client - INFO - Fetching quotes for batch 1: 50 symbols
fyers_client - INFO - Fetching quotes for batch 2: 50 symbols
fyers_client - INFO - Fetching quotes for batch 3: 50 symbols
fyers_client - INFO - Fetching quotes for batch 4: 50 symbols
fyers_client - INFO - Fetching quotes for batch 5: 50 symbols
fyers_client - INFO - Fetching quotes for batch 6: 50 symbols
fyers_client - INFO - Fetching quotes for batch 7: 50 symbols
fyers_client - INFO - Fetching quotes for batch 8: 50 symbols
fyers_client - INFO - Fetching quotes for batch 9: 50 symbols
fyers_client - INFO - Fetching quotes for batch 10: 50 symbols
fyers_client - INFO - Fetching quotes for batch 11: 50 symbols
fyers_client - INFO - Fetching quotes for batch 12: 50 symbols
fyers_client - INFO - Fetching quotes for batch 13: 50 symbols
fyers_client - INFO - Fetching quotes for batch 14: 50 symbols
fyers_client - INFO - Fetching quotes for batch 15: 50 symbols
fyers_client - INFO - Fetching quotes for batch 16: 50 symbols
fyers_client - INFO - Fetching quotes for batch 17: 50 symbols
fyers_client - INFO - Fetching quotes for batch 18: 50 symbols
fyers_client - INFO - Fetching quotes for batch 19: 50 symbols
fyers_client - INFO - Fetching quotes for batch 20: 50 symbols
fyers_client - INFO - Fetching quotes for batch 21: 50 symbols
fyers_client - INFO - Fetching quotes for batch 22: 50 symbols
fyers_client - INFO - Fetching quotes for batch 23: 50 symbols
fyers_client - INFO - Fetching quotes for batch 24: 50 symbols
fyers_client - INFO - Fetching quotes for batch 25: 50 symbols
fyers_client - INFO - Fetching quotes for batch 26: 50 symbols
fyers_client - INFO - Fetching quotes for batch 27: 50 symbols
fyers_client - INFO - Fetching quotes for batch 28: 50 symbols
fyers_client - INFO - Fetching quotes for batch 29: 50 symbols
fyers_client - INFO - Fetching quotes for batch 30: 50 symbols
fyers_client - INFO - Fetching quotes for batch 31: 50 symbols
fyers_client - INFO - Fetching quotes for batch 32: 50 symbols
fyers_client - INFO - Fetching quotes for batch 33: 50 symbols
fyers_client - INFO - Fetching quotes for batch 34: 50 symbols
fyers_client - INFO - Fetching quotes for batch 35: 50 symbols
fyers_client - INFO - Fetching quotes for batch 36: 50 symbols
fyers_client - INFO - Fetching quotes for batch 37: 50 symbols
fyers_client - INFO - Fetching quotes for batch 38: 50 symbols
fyers_client - INFO - Fetching quotes for batch 39: 50 symbols
fyers_client - INFO - Fetching quotes for batch 40: 50 symbols
fyers_client - INFO - Fetching quotes for batch 41: 50 symbols
fyers_client - INFO - Fetching quotes for batch 42: 50 symbols
fyers_client - INFO - Fetching quotes for batch 43: 50 symbols
fyers_client - INFO - Fetching quotes for batch 44: 50 symbols
fyers_client - INFO - Fetching quotes for batch 45: 50 symbols
fyers_client - INFO - Fetching quotes for batch 46: 50 symbols
fyers_client - INFO - Fetching quotes for batch 47: 50 symbols
fyers_client - INFO - Fetching quotes for batch 48: 50 symbols
fyers_client - INFO - Fetching quotes for batch 49: 50 symbols
fyers_client - INFO - Fetching quotes for batch 50: 50 symbols
fyers_client - INFO - Fetching quotes for batch 51: 50 symbols
fyers_client - INFO - Fetching quotes for batch 52: 50 symbols
fyers_client - INFO - Fetching quotes for batch 53: 50 symbols
fyers_client - INFO - Fetching quotes for batch 54: 50 symbols
fyers_client - INFO - Fetching quotes for batch 55: 50 symbols
fyers_client - INFO - Fetching quotes for batch 56: 50 symbols
fyers_client - INFO - Fetching quotes for batch 57: 50 symbols
fyers_client - INFO - Fetching quotes for batch 58: 50 symbols
fyers_client - INFO - Fetching quotes for batch 59: 50 symbols
fyers_client - INFO - Fetching quotes for batch 60: 50 symbols
fyers_client - INFO - Fetching quotes for batch 61: 50 symbols
fyers_client - INFO - Fetching quotes for batch 62: 50 symbols
fyers_client - INFO - Fetching quotes for batch 63: 50 symbols
fyers_client - INFO - Fetching quotes for batch 64: 50 symbols
fyers_client - INFO - Fetching quotes for batch 65: 50 symbols
fyers_client - INFO - Fetching quotes for batch 66: 50 symbols
fyers_client - INFO - Fetching quotes for batch 67: 50 symbols
fyers_client - INFO - Fetching quotes for batch 68: 50 symbols
fyers_client - INFO - Fetching quotes for batch 69: 50 symbols
fyers_client - INFO - Fetching quotes for batch 70: 50 symbols
fyers_client - INFO - Fetching quotes for batch 71: 50 symbols
fyers_client - INFO - Fetching quotes for batch 72: 6 symbols
fyers_client - INFO - Successfully fetched market data for 3556 symbols
index_scanner - INFO - Received market data for 3556 symbols
index_scanner - INFO - Applying filters...
index_scanner - INFO - Applying filters - Volume Range: 35-*********, LTP Range: 2500-9000
index_scanner - INFO - MAE Analysis: Enabled
index_scanner - INFO - MAE Length: 9, Source: close, Offset: 0, Smoothing: ema(9)
index_scanner - INFO - Timeframe - Interval: 60, Days: 15
index_scanner - INFO - First symbol for BANKNIFTY JUL: NSE:BANKNIFTY25JUL48500CE (LTP: 8869.1, Volume: 385)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL48500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 32 OHLC data points for NSE:BANKNIFTY25JUL48500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL49000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:BANKNIFTY25JUL49000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL49500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 55 OHLC data points for NSE:BANKNIFTY25JUL49500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL50000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL50000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL51000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL51500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 63 OHLC data points for NSE:BANKNIFTY25JUL51500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL52000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL52000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL52500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 63 OHLC data points for NSE:BANKNIFTY25JUL52500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL53000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL53500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:BANKNIFTY25JUL53500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54200CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 31 OHLC data points for NSE:BANKNIFTY25JUL54200CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54300CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 28 OHLC data points for NSE:BANKNIFTY25JUL54300CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54400CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 26 OHLC data points for NSE:BANKNIFTY25JUL54400CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL54500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54700CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 18 OHLC data points for NSE:BANKNIFTY25JUL54700CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL54800CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 35 OHLC data points for NSE:BANKNIFTY25JUL54800CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL59700PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 12 OHLC data points for NSE:BANKNIFTY25JUL59700PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL59900PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 13 OHLC data points for NSE:BANKNIFTY25JUL59900PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL60000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25JUL60000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL61000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 83 OHLC data points for NSE:BANKNIFTY25JUL61000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL62000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 70 OHLC data points for NSE:BANKNIFTY25JUL62000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL63000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 77 OHLC data points for NSE:BANKNIFTY25JUL63000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL64000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 61 OHLC data points for NSE:BANKNIFTY25JUL64000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25JUL64500PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 27 OHLC data points for NSE:BANKNIFTY25JUL64500PE
index_scanner - INFO - First symbol for NIFTY JUL: NSE:NIFTY25JUL22300CE (LTP: 3200.0, Volume: 825)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22300CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 68 OHLC data points for NSE:NIFTY25JUL22300CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22350CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:NIFTY25JUL22350CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:NIFTY25JUL22350CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22400CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 32 OHLC data points for NSE:NIFTY25JUL22400CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25JUL22500CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22550CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 7 OHLC data points for NSE:NIFTY25JUL22550CE
index_scanner - WARNING - Insufficient historical data for MAE calculation: NSE:NIFTY25JUL22550CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22600CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 30 OHLC data points for NSE:NIFTY25JUL22600CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22650CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 17 OHLC data points for NSE:NIFTY25JUL22650CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22700CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 45 OHLC data points for NSE:NIFTY25JUL22700CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22800CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 34 OHLC data points for NSE:NIFTY25JUL22800CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL22900CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 25 OHLC data points for NSE:NIFTY25JUL22900CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25JUL23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:NIFTY25JUL23000CE
index_scanner - INFO - First symbol for BANKNIFTY AUG: NSE:BANKNIFTY25AUG50000CE (LTP: 7630.0, Volume: 420)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG50000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 53 OHLC data points for NSE:BANKNIFTY25AUG50000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG53000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 48 OHLC data points for NSE:BANKNIFTY25AUG53000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 67 OHLC data points for NSE:BANKNIFTY25AUG54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG55000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 82 OHLC data points for NSE:BANKNIFTY25AUG55000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG60000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 46 OHLC data points for NSE:BANKNIFTY25AUG60000PE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25AUG62000PE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 16 OHLC data points for NSE:BANKNIFTY25AUG62000PE
index_scanner - INFO - First symbol for NIFTY AUG: NSE:NIFTY25AUG23000CE (LTP: 2613.6, Volume: 600)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25AUG23000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 79 OHLC data points for NSE:NIFTY25AUG23000CE
index_scanner - INFO - First symbol for BANKNIFTY SEP: NSE:BANKNIFTY25SEP55000CE (LTP: 3149.75, Volume: 35)
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP55000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 9 OHLC data points for NSE:BANKNIFTY25SEP55000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP52500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 54 OHLC data points for NSE:BANKNIFTY25SEP52500CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP54000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 60 OHLC data points for NSE:BANKNIFTY25SEP54000CE
fyers_client - INFO - Fetching historical data for NSE:BANKNIFTY25SEP55500CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 84 OHLC data points for NSE:BANKNIFTY25SEP55500CE
index_scanner - INFO - First symbol for NIFTY SEP: NSE:NIFTY25SEP17000CE (LTP: 8595.2, Volume: 3000)
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP17000CE - Interval: 60, Days: 15
fyers_client - INFO - Successfully fetched 50 OHLC data points for NSE:NIFTY25SEP17000CE
fyers_client - INFO - Fetching historical data for NSE:NIFTY25SEP18000CE - Interval: 60, Days: 15
