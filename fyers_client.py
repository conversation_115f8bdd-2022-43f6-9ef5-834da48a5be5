"""
Fyers API client for fetching market data.
Handles authentication and provides methods to get quotes for symbols.
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from fyers_config import FyersConfig

logger = logging.getLogger(__name__)

# Interval mapping for Fyers API
INTERVAL_MAP = {
    "1D": "1D",  # Days
    "1W": "1D",  # Days
    "1": "1",    # Minutes
    "3": "3",
    "5": "5",
    "15": "15",
    "30": "30",
    "60": "60"   # Minutes
}

@dataclass
class MarketData:
    """Data class to represent market data for a symbol."""
    symbol: str
    ltp: float
    volume: int
    open_price: float
    high: float
    low: float
    close: float
    prev_close: float
    change: float
    change_percent: float

@dataclass
class OHLCData:
    """Data class to represent OHLC historical data."""
    timestamp: int
    open: float
    high: float
    low: float
    close: float
    volume: int

class FyersClient:
    """Client for interacting with Fyers API."""
    
    def __init__(self, env_path: str = None):
        """
        Initialize Fyers client.
        
        Args:
            env_path: Path to environment file containing Fyers credentials
        """
        self.fyers_config = FyersConfig(env_path=env_path)
        self.fyers_api = None
        self.access_token = None
        
    def authenticate(self) -> bool:
        """
        Authenticate with Fyers API.
        
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            logger.info("Starting Fyers authentication...")
            self.access_token = self.fyers_config.authenticate()
            
            if self.access_token:
                # Initialize Fyers API client
                from fyers_apiv3.fyersModel import FyersModel
                
                self.fyers_api = FyersModel(
                    client_id=self.fyers_config.config["client_id"],
                    is_async=False,
                    token=self.access_token
                )
                
                logger.info("Fyers authentication successful")
                return True
            else:
                logger.error("Failed to get access token")
                return False
                
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return False
    
    def get_quotes(self, symbols: List[str]) -> Dict[str, MarketData]:
        """
        Get market quotes for multiple symbols.
        
        Args:
            symbols: List of symbol strings in NSE format
            
        Returns:
            Dictionary mapping symbol to MarketData object
        """
        if not self.fyers_api:
            logger.error("Fyers API not initialized. Please authenticate first.")
            return {}
        
        market_data = {}
        
        try:
            # Fyers API can handle multiple symbols in one call
            # But we'll process in batches to avoid API limits
            batch_size = 50
            
            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]
                
                logger.info(f"Fetching quotes for batch {i//batch_size + 1}: {len(batch_symbols)} symbols")
                
                # Prepare symbols for Fyers API (comma-separated)
                symbol_string = ",".join(batch_symbols)
                
                # Get quotes from Fyers API
                response = self.fyers_api.quotes({"symbols": symbol_string})
                #logger.info(f"Raw Fyers API response: {response}")  # <-- Print the raw response for debugging

                if response and response.get("code") == 200:
                    quotes_data = response.get("d", {})

                    # Handle case where quotes_data might be a list instead of dict
                    if isinstance(quotes_data, list):
                        # If it's a list, try to match symbols by position or find symbol field
                        for i, symbol_data in enumerate(quotes_data):
                            if i < len(batch_symbols) and isinstance(symbol_data, dict):
                                symbol = batch_symbols[i]
                                try:
                                    # Parse market data from Fyers response
                                    market_data[symbol] = self._parse_market_data(symbol, symbol_data)
                                except Exception as e:
                                    logger.warning(f"Failed to parse data for {symbol}: {e}")
                    elif isinstance(quotes_data, dict):
                        # Original logic for dict response
                        for symbol in batch_symbols:
                            symbol_data = quotes_data.get(symbol)

                            if symbol_data:
                                try:
                                    # Parse market data from Fyers response
                                    market_data[symbol] = self._parse_market_data(symbol, symbol_data)
                                except Exception as e:
                                    logger.warning(f"Failed to parse data for {symbol}: {e}")
                            else:
                                logger.warning(f"No data received for symbol: {symbol}")
                    else:
                        logger.warning(f"Unexpected quotes_data format: {type(quotes_data)}")
                else:
                    logger.error(f"Failed to get quotes for batch: {response}")
                    
        except Exception as e:
            logger.error(f"Error fetching quotes: {e}")
            
        logger.info(f"Successfully fetched market data for {len(market_data)} symbols")
        return market_data
    
    def _parse_market_data(self, symbol: str, data: Dict[str, Any]) -> MarketData:
        """
        Parse market data from Fyers API response.
        
        Args:
            symbol: Symbol string
            data: Market data from Fyers API
            
        Returns:
            MarketData object
        """
        try:
            # If 'v' key exists and is a dict, use its fields for OHLC etc.
            v = data.get('v', {})
            if isinstance(v, dict) and v:
                ltp = float(v.get('lp', 0))
                volume = int(v.get('volume', 0))
                open_price = float(v.get('open_price', 0))
                high = float(v.get('high_price', 0))
                low = float(v.get('low_price', 0))
                close = float(v.get('lp', 0))  # Fyers does not provide close, use ltp
                prev_close = float(v.get('prev_close_price', open_price))
                change = ltp - prev_close if prev_close > 0 else 0
                change_percent = (change / prev_close * 100) if prev_close > 0 else 0
            else:
                # Fallback to old logic if 'v' is not present
                ltp = float(data.get("lp", 0))
                volume_data = data.get("v", 0)
                if isinstance(volume_data, dict):
                    volume = int(volume_data.get("volume", 0))
                else:
                    volume = int(volume_data) if volume_data else 0
                open_price = float(data.get("o", 0))
                high = float(data.get("h", 0))
                low = float(data.get("l", 0))
                close = float(data.get("c", 0))
                prev_close = float(data.get("prev_close", close))
                change = ltp - prev_close if prev_close > 0 else 0
                change_percent = (change / prev_close * 100) if prev_close > 0 else 0
            return MarketData(
                symbol=symbol,
                ltp=ltp,
                volume=volume,
                open_price=open_price,
                high=high,
                low=low,
                close=close,
                prev_close=prev_close,
                change=change,
                change_percent=change_percent
            )
        except Exception as e:
            logger.error(f"Error parsing market data for {symbol}: {e}")
            # Return default MarketData with zero values
            return MarketData(
                symbol=symbol,
                ltp=0.0,
                volume=0,
                open_price=0.0,
                high=0.0,
                low=0.0,
                close=0.0,
                prev_close=0.0,
                change=0.0,
                change_percent=0.0
            )
    
    def get_single_quote(self, symbol: str) -> Optional[MarketData]:
        """
        Get market quote for a single symbol.
        
        Args:
            symbol: Symbol string in NSE format
            
        Returns:
            MarketData object or None if failed
        """
        quotes = self.get_quotes([symbol])
        return quotes.get(symbol)
    
    def is_authenticated(self) -> bool:
        """
        Check if client is authenticated.
        
        Returns:
            True if authenticated, False otherwise
        """
        return self.fyers_api is not None and self.access_token is not None
    
    def get_historical_data(self, symbol: str, interval: int, days_to_fetch: int) -> List[OHLCData]:
        """
        Get historical OHLC data for a symbol.
        
        Args:
            symbol: Symbol string in NSE format
            interval: Timeframe interval (1, 3, 5, 15, 30, 60 for minutes, 1D for daily)
            days_to_fetch: Number of days to fetch historical data
            
        Returns:
            List of OHLCData objects
        """
        if not self.fyers_api:
            logger.error("Fyers API not initialized. Please authenticate first.")
            return []
        
        try:
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_to_fetch)

            # Convert to YYYY-MM-DD format as required by Fyers API when date_format=1
            from_date = start_date.strftime('%Y-%m-%d')
            to_date = end_date.strftime('%Y-%m-%d')

            # Map interval to Fyers format
            fyers_interval = str(interval) if interval != 1440 else "1D"
            if str(interval) in INTERVAL_MAP:
                fyers_interval = INTERVAL_MAP[str(interval)]

            logger.info(f"Fetching historical data for {symbol} - Interval: {fyers_interval}, Days: {days_to_fetch}")

            # Prepare request data
            data = {
                "symbol": symbol,
                "resolution": fyers_interval,
                "date_format": "1",  # Date string format (YYYY-MM-DD)
                "range_from": from_date,
                "range_to": to_date,
                "cont_flag": "1"
            }

            # Get historical data from Fyers API
            response = self.fyers_api.history(data)
            
            if response and response.get("code") == 200:
                candles = response.get("candles", [])
                ohlc_data = []
                
                for candle in candles:
                    if len(candle) >= 6:  # timestamp, open, high, low, close, volume
                        ohlc_data.append(OHLCData(
                            timestamp=int(candle[0]),
                            open=float(candle[1]),
                            high=float(candle[2]),
                            low=float(candle[3]),
                            close=float(candle[4]),
                            volume=int(candle[5])
                        ))
                
                logger.info(f"Successfully fetched {len(ohlc_data)} OHLC data points for {symbol}")
                return ohlc_data
            else:
                logger.error(f"Failed to get historical data for {symbol}: {response}")
                return []
                
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {e}")
            return []
