{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:51,849+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:52,069+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:52,273+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:52,479+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:52,589+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:52,785+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:52,990+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:53,196+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:53,403+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:53,546+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:53,708+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:53,963+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:54,220+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:54,423+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:54,629+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:54,834+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:55,039+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:55,243+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:55,451+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:55,654+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:55,841+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:56,165+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:56,269+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:56,471+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:56,779+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:57,086+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:57,292+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:57,702+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:57,844+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:58,009+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:58,214+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:58,527+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:58,827+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:59,032+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:59,135+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:59,339+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:59,544+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:59,750+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:58:59,852+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:59:00,056+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:59:00,363+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:59:00,671+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:59:00,838+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 15:59:00,977+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:10,620+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:10,783+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:11,030+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:11,337+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:11,439+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:11,702+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:11,848+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:12,053+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:12,360+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:12,463+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:12,567+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:12,776+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:12,881+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:13,086+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:13,386+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:13,590+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:13,692+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:13,805+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:14,102+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:14,204+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:14,304+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:14,581+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:14,716+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:14,921+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:15,127+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:15,433+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:15,536+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:15,661+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:15,945+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:16,149+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:16,458+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:16,560+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:16,764+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:16,972+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:17,212+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:17,373+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:17,481+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:17,619+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:17,789+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:17,919+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:18,044+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:18,300+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:18,394+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":-50,"data":{"date_format":"date timestamps (YYYY-MM-DD) needed for range_from and range_to since date_format is 1"},"message":"Invalid input","s":"error"}},"timestamp":"2025-07-01 16:01:18,531+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:13,644+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:13,952+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:14,361+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:14,668+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:14,976+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:15,181+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:15,590+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:15,898+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:16,204+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:18:16,410+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:12,730+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:12,833+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:13,038+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:13,243+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:13,788+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:14,382+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:15,004+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:15,629+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:16,189+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-01 19:19:16,580+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:22,536+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:22,640+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:22,743+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:23,187+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:23,561+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:23,963+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:24,395+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:145] fyersModel","message":{"API":"/history","Error":{"code":429,"message":"request limit reached","s":"error"}},"timestamp":"2025-07-02 01:48:24,804+0530","service":"FyersAPI"}
