import pandas as pd
import ta

# --- MAEAnalyzer: Moving Average Exponential using ta library ---
class MAEAnalyzer:
    def calculate_mae_manual(self, ohlc_data):
        """
        Calculate MAE (double EMA) manually without using ta library.
        Returns: pd.Series of MAE values
        """
        import numpy as np
        series = self.get_source_series(ohlc_data)
        # First smoothing (SMA/WMA/EMA)
        if self.smoothing_line == 'sma':
            smoothed = series.rolling(window=self.smoothing_length, min_periods=self.smoothing_length).mean()
        elif self.smoothing_line == 'wma':
            def wma(x):
                weights = np.arange(1, len(x)+1)
                return np.dot(x, weights) / weights.sum()
            smoothed = series.rolling(window=self.smoothing_length, min_periods=self.smoothing_length).apply(wma, raw=True)
        else:
            # Manual EMA
            alpha = 2 / (self.smoothing_length + 1)
            smoothed = series.ewm(alpha=alpha, adjust=False, min_periods=self.smoothing_length).mean()
        # Second EMA (MAE)
        alpha2 = 2 / (self.length + 1)
        mae = smoothed.ewm(alpha=alpha2, adjust=False, min_periods=self.length).mean()
        if self.offset != 0:
            mae = mae.shift(self.offset)
        return mae
    """Analyzer for Moving Average Exponential (MAE) using ta library and config options."""
    def __init__(self, length=9, source='close', offset=0, smoothing_line='ema', smoothing_length=9):
        self.length = length
        self.source = source
        self.offset = offset
        self.smoothing_line = smoothing_line
        self.smoothing_length = smoothing_length

    def get_source_series(self, ohlc_data):
        """
        Extracts the correct price series from OHLC data based on config (open, high, low, close, hl2, hlc3, ohlc4).
        ohlc_data: List[OHLCData] or Dict with keys open, high, low, close
        Returns: pd.Series
        """
        df = pd.DataFrame([{k: getattr(row, k) for k in ['open', 'high', 'low', 'close']} for row in ohlc_data])
        if self.source == 'open':
            return df['open']
        elif self.source == 'high':
            return df['high']
        elif self.source == 'low':
            return df['low']
        elif self.source == 'hl2':
            return (df['high'] + df['low']) / 2
        elif self.source == 'hlc3':
            return (df['high'] + df['low'] + df['close']) / 3
        elif self.source == 'ohlc4':
            return (df['open'] + df['high'] + df['low'] + df['close']) / 4
        else:
            return df['close']

    def calculate_mae(self, ohlc_data):
        """
        Calculate MAE using ta library and config options.
        Returns: pd.Series of MAE values
        """
        series = self.get_source_series(ohlc_data)
        if self.smoothing_line == 'sma':
            smoothed = ta.trend.sma_indicator(series, window=self.smoothing_length, fillna=False)
        elif self.smoothing_line == 'wma':
            smoothed = ta.trend.wma_indicator(series, window=self.smoothing_length, fillna=False)
        else:
            smoothed = ta.trend.ema_indicator(series, window=self.smoothing_length, fillna=False)
        mae = ta.trend.ema_indicator(smoothed, window=self.length, fillna=False)
        if self.offset != 0:
            mae = mae.shift(self.offset)
        return mae

    def is_price_passing_through_mae(self, ohlc_data, current_price, tolerance=0.5):
        """
        Check if current price is within tolerance % of latest MAE value.
        Returns: bool
        """
        mae = self.calculate_mae(ohlc_data)
        if mae.empty or pd.isna(mae.iloc[-1]):
            return False
        latest_mae = mae.iloc[-1]
        tol = abs(latest_mae) * (tolerance / 100)
        return (latest_mae - tol) <= current_price <= (latest_mae + tol)
