"""
Test script to verify EMA integration with the new filtering logic.
"""
import logging
from config_loader import get_config
from technical_indicators import <PERSON><PERSON><PERSON>yzer
from fyers_client import OHLCData
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("ema_test")

def test_mae_integration():
    """Test the MAE integration functionality with focused coverage."""
    # Load configuration
    config = get_config()
    logger.info("Configuration loaded successfully")
    # --- FYERS LIVE SYMBOL TESTS ---
    from fyers_client import FyersClient
    symbols = [
        "NSE:NIFTY25JUL22400CE",
        "NSE:NIFTY25JUL22650CE",
        "NSE:NIFTY25JUL22700CE",
        "NSE:BANKNIFTY25JUL54800CE",
        "NSE:BANKNIFTY25AUG50000CE",
        "NSE:BANKNIFTY25SEP52500CE"
    ]
    logger.info("\n--- FYERS LIVE SYMBOL MAE TESTS ---")
    fyers = FyersClient()
    assert fyers.authenticate(), "Fyers authentication failed!"
    mae_analyzer = MAEAnalyzer(
        length=config.mae_length,
        source=config.mae_source,
        offset=config.mae_offset,
        smoothing_line=config.mae_smoothing_line,
        smoothing_length=config.mae_smoothing_length
    )
    for symbol in symbols:
        logger.info(f"\nTesting symbol: {symbol}")
        ohlc_data = fyers.get_historical_data(symbol, interval=config.timeframe_interval, days_to_fetch=config.days_to_fetch)
        if not ohlc_data or len(ohlc_data) < config.mae_length:
            logger.warning(f"Not enough OHLC data for {symbol}, skipping.")
            continue
        mae_series = mae_analyzer.calculate_mae(ohlc_data)
        mae_manual_series = mae_analyzer.calculate_mae_manual(ohlc_data)
        logger.info(f"MAE last two (ta): {mae_series.iloc[-2:]}")
        logger.info(f"MAE last two (manual): {mae_manual_series.iloc[-2:]}")
        final_mae = mae_series.iloc[-1]
        final_mae_manual = mae_manual_series.iloc[-1]
        logger.info(f"Final MAE value (ta): {final_mae}")
        logger.info(f"Final MAE value (manual): {final_mae_manual}")
        if symbol == "NSE:NIFTY25JUL22400CE":
            expected_mae = 3153.90
            assert abs(final_mae - expected_mae) < 2.0, f"TA MAE for {symbol} differs from expected: {final_mae} vs {expected_mae}"
            assert abs(final_mae_manual - expected_mae) < 2.0, f"Manual MAE for {symbol} differs from expected: {final_mae_manual} vs {expected_mae}"
        last_close = ohlc_data[-1].close
        passing = mae_analyzer.is_price_passing_through_mae(ohlc_data, last_close)
        logger.info(f"Passing through MAE: {passing}")
        assert mae_series.notna().any(), f"MAE calculation failed for {symbol}"

    logger.info(f"MAE enabled: {config.mae_enabled}")
    logger.info(f"MAE length: {config.mae_length}")
    logger.info(f"MAE source: {config.mae_source}")
    logger.info(f"MAE offset: {config.mae_offset}")
    logger.info(f"MAE smoothing_line: {config.mae_smoothing_line}")
    logger.info(f"MAE smoothing_length: {config.mae_smoothing_length}")

    # Create dummy OHLC data for testing
    class DummyOHLC:
        def __init__(self, open, high, low, close):
            self.open = open
            self.high = high
            self.low = low
            self.close = close

    # --- FLAT Test ---
    flat_ohlc = [DummyOHLC(10, 10, 10, 10) for _ in range(15)]
    mae_analyzer = MAEAnalyzer(
        length=config.mae_length,
        source=config.mae_source,
        offset=config.mae_offset,
        smoothing_line=config.mae_smoothing_line,
        smoothing_length=config.mae_smoothing_length
    )
    mae_series = mae_analyzer.calculate_mae(flat_ohlc)
    logger.info("\n--- FLAT Test ---")
    logger.info(f"MAE last two: {mae_series.iloc[-2:]}")
    logger.info(f"Passing through MAE: {mae_analyzer.is_price_passing_through_mae(flat_ohlc, 10)}")

    # --- SPIKE Test ---
    spike_ohlc = [DummyOHLC(10, 10, 10, 10) for _ in range(15)] + [DummyOHLC(10, 100, 10, 100)]
    mae_series = mae_analyzer.calculate_mae(spike_ohlc)
    logger.info("\n--- SPIKE Test ---")
    logger.info(f"MAE last two: {mae_series.iloc[-2:]}")
    logger.info(f"Passing through MAE: {mae_analyzer.is_price_passing_through_mae(spike_ohlc, 100)}")

    # --- DROP Test ---
    drop_ohlc = [DummyOHLC(100, 100, 100, 100) for _ in range(15)] + [DummyOHLC(100, 10, 10, 10)]
    mae_series = mae_analyzer.calculate_mae(drop_ohlc)
    logger.info("\n--- DROP Test ---")
    logger.info(f"MAE last two: {mae_series.iloc[-2:]}")
    logger.info(f"Passing through MAE: {mae_analyzer.is_price_passing_through_mae(drop_ohlc, 10)}")
    # (MAE test does not use realistic_ema, so these lines are removed)

    logger.info("EMA integration test completed successfully!")

if __name__ == "__main__":
    test_mae_integration()